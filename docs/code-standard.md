# 光年小卖部项目代码规范

## 目录
1. [通用规范](#通用规范)
2. [前端开发规范](#前端开发规范)
3. [后端开发规范](#后端开发规范)
4. [数据库规范](#数据库规范)
5. [API规范](#api规范)
6. [Git工作流规范](#git工作流规范)
7. [文档规范](#文档规范)

## 通用规范

### 命名规范
- 所有代码文件、目录使用有意义的英文命名，禁止使用中文、拼音或无意义字符
- 变量和函数命名应体现其用途，禁止使用a、b、temp等无意义命名
- 常量使用全大写，单词间用下划线分隔，如：`MAX_COUNT`
- 避免使用保留字作为变量名

### 注释规范
- 重要的代码块应有注释说明
- 复杂逻辑必须有注释
- 注释应简洁明了，说明代码的目的而非过程
- API接口必须有完整的Swagger文档注释

### 编码规范
- 使用UTF-8编码
- 使用LF（\n）作为行结束符
- 文件末尾应有一个空行

## 前端开发规范

### Vue组件规范
- 组件名称使用PascalCase格式，如：`ProductCard`
- 单文件组件的文件名应与组件名一致
- 组件prop定义应当尽量详细，至少需要指定类型
- 组件事件名称使用kebab-case格式，如：`item-click`

```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
export default {
  name: 'ComponentName',
  props: {
    propName: {
      type: String,
      required: true,
      default: ''
    }
  },
  data() {
    return {
      // 数据
    }
  },
  methods: {
    handleEvent() {
      this.$emit('item-click')
    }
  }
}
</script>

<style scoped>
/* CSS样式 */
</style>
```

### JavaScript规范
- 使用ES6+语法
- 使用`const`和`let`替代`var`
- 优先使用箭头函数
- 使用async/await处理异步，避免回调地狱
- 模块导入导出使用ES6语法

```javascript
// 导入
import { mapState } from 'vuex'
import UserCard from '@/components/UserCard'

// 变量声明
const MAX_ITEMS = 10
let count = 0

// 箭头函数
const handleClick = () => {
  // 处理逻辑
}

// 异步处理
const fetchData = async () => {
  try {
    const response = await api.getData()
    return response.data
  } catch (error) {
    console.error('获取数据失败', error)
    return null
  }
}
```

### CSS规范
- 使用scoped CSS或CSS Modules避免样式污染
- 使用BEM命名方法论
- 优先使用类选择器，避免使用ID选择器和标签选择器
- 颜色值使用变量定义，便于主题切换

```css
/* BEM命名示例 */
.product-card {}
.product-card__title {}
.product-card__price {}
.product-card--featured {}

/* 颜色变量 */
:root {
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --text-color: #333;
  --background-color: #f8f8f8;
}
```

## 后端开发规范

### 代码结构
- 遵循MVC架构模式
- 按功能模块组织代码
- 复杂业务逻辑应抽象为服务层
- 工具函数应放在utils目录中

```
server/
  ├── controllers/      # 控制器
  ├── models/           # 数据模型
  ├── routes/           # 路由
  ├── services/         # 业务服务
  ├── middlewares/      # 中间件
  ├── utils/            # 工具函数
  ├── config/           # 配置文件
  └── server.js         # 入口文件
```

### API设计
- 使用RESTful风格设计API
- URL使用名词而非动词，如`/api/products`而非`/api/getProducts`
- 使用HTTP方法表示操作：
  - GET：获取资源
  - POST：创建资源
  - PUT：更新资源
  - DELETE：删除资源
- 返回适当的HTTP状态码
- 统一响应格式

```javascript
// 统一响应格式示例
{
  "success": true,       // 操作是否成功
  "message": "获取成功", // 消息提示
  "data": {},           // 响应数据
  "error": null         // 错误信息
}
```

### 错误处理
- 使用try-catch捕获异步错误
- 统一错误处理中间件
- 记录错误日志
- 返回友好的错误消息

```javascript
// 错误处理示例
try {
  const product = await Product.findById(id);
  if (!product) {
    return res.status(404).json({
      success: false,
      message: '商品不存在',
      error: 'NOT_FOUND'
    });
  }
  // 处理业务逻辑
} catch (error) {
  console.error('获取商品失败', error);
  return res.status(500).json({
    success: false,
    message: '服务器错误',
    error: 'SERVER_ERROR'
  });
}
```

## 数据库规范

### MongoDB设计规范
- 集合名称使用复数形式，如`products`而非`product`
- 字段名使用驼峰命名法，如`createdAt`
- 创建必要的索引提高查询性能
- 合理使用嵌入和引用关系
- 添加创建时间和更新时间字段

```javascript
// 模型示例
const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  description: String,
  images: [String],
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  }
}, { timestamps: true });

// 创建索引
productSchema.index({ name: 'text', description: 'text' });
productSchema.index({ category: 1 });
productSchema.index({ createdAt: -1 });
```

## API规范

### API文档
- 使用Swagger记录所有API
- API文档应包含：
  - 接口描述
  - 请求方法
  - 请求参数
  - 响应格式
  - 错误码
  - 权限要求

### 版本控制
- API路径包含版本号，如`/api/v1/products`
- 重大变更应提升版本号而非修改现有API

### 安全规范
- 敏感API使用JWT认证
- 实施请求频率限制
- 验证所有用户输入
- 使用HTTPS传输

## Git工作流规范

### 分支管理
- `main`分支：生产环境代码，受保护
- `develop`分支：开发环境代码
- 功能分支：`feature/功能名称`
- 修复分支：`bugfix/问题描述`
- 发布分支：`release/版本号`

### 提交规范
- 提交信息应清晰描述变更内容
- 使用约定式提交格式：
  - `feat`: 新功能
  - `fix`: 修复问题
  - `docs`: 文档变更
  - `style`: 代码格式变更
  - `refactor`: 代码重构
  - `perf`: 性能优化
  - `test`: 测试相关
  - `chore`: 构建过程或辅助工具变动

```
feat(product): 添加商品排序功能
fix(order): 修复订单金额计算错误
docs(api): 更新API文档
```

### 代码审查
- 所有合并请求必须经过代码审查
- 代码审查应关注：
  - 代码质量
  - 安全问题
  - 性能问题
  - 是否符合规范

## 文档规范

### 必要文档
- README.md：项目概述
- API文档：接口文档
- 部署文档：部署说明
- 开发文档：开发环境搭建、常见问题等

### 文档格式
- 使用Markdown格式
- 结构清晰，使用标题层级
- 包含目录和索引
- 代码示例使用代码块并指定语言

### 更新维护
- 代码变更时同步更新相关文档
- 定期检查文档有效性
- 将文档纳入版本控制
