<template>
  <div class="payment-qrcode-container">
    <div class="payment-step">
      <div class="step-header">
        <div class="step-number">1</div>
        <div class="step-title">支付宝扫码支付</div>
      </div>
      <div class="qrcode-section">
        <div class="qrcode-wrapper" @click="handleQRCodeClick">
          <img :src="qrCodeUrl" class="qrcode-image" alt="支付宝收款码" />
          <div class="qrcode-hover-tip">点击查看大图</div>
        </div>
        <div class="payment-info">
          <p class="payment-amount">支付金额: <span class="amount">¥{{ amount.toFixed(2) }}</span></p>
          <p class="payment-notice">请使用支付宝扫描上方二维码支付</p>
          <p class="payment-tips">付款后请务必保留支付凭证截图，提交下一步将需要上传支付截图</p>
        </div>
      </div>
    </div>

    <div class="payment-step">
      <div class="step-header">
        <div class="step-number">2</div>
        <div class="step-title">上传支付截图</div>
      </div>
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :on-preview="handlePreview"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
          :limit="1"
          list-type="picture-card"
          :auto-upload="true"
          :name="uploadFieldName"
          accept="image/*"
        >
          <el-icon><Plus /></el-icon>
          <template #tip>
            <div class="el-upload__tip">
              请上传支付宝支付成功截图，仅支持图片格式
            </div>
          </template>
        </el-upload>
      </div>
    </div>

    <div class="payment-actions">
      <el-button @click="$emit('cancel')" plain>取消</el-button>
      <el-button 
        type="primary" 
        @click="confirmPayment" 
        :disabled="!paymentProofUrl"
        :loading="submitting"
      >
        确认支付完成
      </el-button>
    </div>
    
    <!-- 预览图片对话框 -->
    <el-dialog v-model="dialogVisible" :title="isQRCodePreview ? '支付宝收款码' : '支付截图预览'" width="400px">
      <div class="preview-container">
        <img :src="previewImageUrl" :alt="isQRCodePreview ? '支付宝收款码' : '支付截图预览'" style="width: 100%;" />
        <div class="preview-actions" v-if="isQRCodePreview">
          <el-button type="primary" @click="downloadQRCode">
            <el-icon><Download /></el-icon>
            下载二维码
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '../stores/auth';
import { ElMessage } from 'element-plus';
import { Plus, Download } from '@element-plus/icons-vue';
import axios from 'axios'; // 导入axios
import { getPaymentQRCode } from '../api/system'; // 导入获取支付码的API方法

const props = defineProps({
  amount: {
    type: Number,
    required: true
  }
});

const emit = defineEmits(['payment-confirmed', 'cancel']);

const authStore = useAuthStore();
const uploadRef = ref(null);
const submitting = ref(false);
const paymentProofUrl = ref('');
const dialogVisible = ref(false);
const previewImageUrl = ref('');
const loading = ref(false);
const isQRCodePreview = ref(false);
const retryCount = ref(0);

// 支付宝收款码图片URL，默认占位图
const qrCodeUrl = ref('public/images/payment/pay.png');

// 定义API基础URL
const apiBaseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

// 获取支付码图片
const fetchPaymentQRCode = async () => {
  try {
    loading.value = true;
    console.log('开始获取支付码图片...');
    const response = await getPaymentQRCode();
    console.log('获取支付码响应:', response);
    
    if (response && response.qrcodeUrl) {
      // 环境检测 - 确保URL与当前环境匹配
      let processedUrl = response.qrcodeUrl;
      console.log('原始收款码URL:', processedUrl);
      
      const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      console.log('是否本地环境:', isLocalhost);
      
      // 确保在本地环境使用localhost
      if (isLocalhost && processedUrl.includes('**************')) {
        console.log('检测到生产URL在本地环境使用，修正为localhost');
        processedUrl = processedUrl.replace(/http:\/\/47\.122\.122\.245(?::\d+)?/g, 'http://localhost:3000');
        console.log('修正后的URL:', processedUrl);
      }
      
      // 添加缓存破坏参数
      const timestamp = new Date().getTime();
      if (!processedUrl.includes('?')) {
        processedUrl += `?t=${timestamp}`;
      } else {
        processedUrl += `&t=${timestamp}`;
      }
      console.log('添加时间戳后的URL:', processedUrl);
      
      qrCodeUrl.value = processedUrl;
      
      // 测试图片加载
      console.log('测试图片加载:', processedUrl);
      
      // 先尝试一个简单的图片预加载
      setTimeout(() => {
        const img = new Image();
        img.onload = () => {
          console.log('支付码图片加载成功', img.width, 'x', img.height);
        };
        img.onerror = (e) => {
          console.error('支付码图片加载失败:', e);
          
          // 再次尝试一个特殊修复 - 尝试直接使用服务器路径
          console.log('尝试使用直接服务器路径访问');
          
          // 从URL中提取文件名
          const filenameMatch = processedUrl.match(/\/([^\/]+)(\?|$)/);
          if (filenameMatch && filenameMatch[1]) {
            const filename = filenameMatch[1];
            const directUrl = `http://localhost:3000/uploads/payment/${filename}?t=${new Date().getTime()}`;
            console.log('尝试直接访问:', directUrl);
            
            // 测试直接URL
            const directImg = new Image();
            directImg.onload = () => {
              console.log('直接URL加载成功');
              qrCodeUrl.value = directUrl;
            };
            directImg.onerror = () => {
              console.error('直接URL也加载失败，使用默认图片');
              qrCodeUrl.value = 'public/images/payment/pay.png';
              ElMessage.warning('支付码图片加载失败，使用默认图片');
            };
            directImg.src = directUrl;
          } else {
            // 无法提取文件名，使用默认图片
            console.log('无法从URL提取文件名，使用默认图片');
            qrCodeUrl.value = 'public/images/payment/pay.png';
            ElMessage.warning('支付码图片加载失败，使用默认图片');
          }
        };
        img.src = processedUrl;
      }, 500);
    } else {
      console.warn('未获取到支付码图片，使用默认图片');
      ElMessage.warning('未获取到支付码图片，使用默认图片');
      qrCodeUrl.value = 'public/images/payment/pay.png';
    }
  } catch (error) {
    console.error('获取支付码失败:', error);
    ElMessage.warning({
      message: '获取支付码图片失败，使用默认图片',
      duration: 3000
    });
    qrCodeUrl.value = 'public/images/payment/pay.png';
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取支付码
onMounted(() => {
  fetchPaymentQRCode();
});

// 计算上传API路径和头信息
const uploadAction = computed(() => {
  const fullUploadUrl = `${apiBaseUrl}/uploads`;
  console.log('设置上传路径为:', fullUploadUrl);
  return fullUploadUrl;
});
const uploadFieldName = 'file'; // 修改字段名与后端一致
const uploadHeaders = computed(() => {
  const token = authStore.token;
  return token ? { Authorization: `Bearer ${token}` } : {};
});

// 处理上传前的验证
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!');
    return false;
  }
  
  return true;
};

// 处理上传成功
const handleUploadSuccess = (response, file) => {
  console.log('支付截图上传成功:', response);
  // 保存上传后的图片URL，适配后端返回的数据格式
  let imageUrl = '';
  
  if (response.errno === 0 && response.data && response.data.url) {
    imageUrl = response.data.url;
  } else if (response.imageUrl) {
    // 兼容另一种可能的返回格式
    imageUrl = response.imageUrl;
  } else if (response.url) {
    // 兼容更简单的返回格式
    imageUrl = response.url;
  } else {
    console.error('意外的响应格式:', response);
    ElMessage.error('支付截图上传成功，但处理响应时出错');
    return;
  }
  
  // 处理图片URL，确保正确的路径格式
  paymentProofUrl.value = processImageUrl(imageUrl);
  console.log('处理后的支付截图URL:', paymentProofUrl.value);
  ElMessage.success('支付截图上传成功');
};

// 处理图片URL，确保正确的路径格式
const processImageUrl = (url) => {
  if (!url) return '';
  
  console.log('处理图片URL:', url);
  
  // 如果是完整的URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // 处理uploads路径，确保使用/api/uploads前缀
  if (url.startsWith('/uploads/')) {
    const newUrl = url.replace(/^\/uploads\//, '/api/uploads/');
    console.log('转换uploads路径:', url, '->', newUrl);
    return newUrl;
  }
  
  // 处理相对路径，如果是相对路径，拼接API基础URL
  if (!url.startsWith('/') && !url.startsWith('http')) {
    const newUrl = `${apiBaseUrl}/${url}`;
    console.log('转换相对路径:', url, '->', newUrl);
    return newUrl;
  }
  
  return url;
};

// 处理上传失败
const handleUploadError = (error) => {
  console.error('支付截图上传失败:', error);
  
  if (error.response && error.response.status === 404) {
    ElMessage.error('上传服务不可用(404错误)，尝试备用上传方式');
    // 尝试使用备用上传方法手动上传
    tryManualUpload();
  } else {
    ElMessage.error(`支付截图上传失败: ${error.message || '未知错误'}`);
  }
};

// 手动上传图片作为备用策略
const tryManualUpload = async () => {
  try {
    const fileInput = document.querySelector('.el-upload input[type="file"]');
    if (!fileInput || !fileInput.files || !fileInput.files[0]) {
      ElMessage.error('未找到要上传的文件');
      return;
    }
    
    const file = fileInput.files[0];
    const formData = new FormData();
    formData.append('file', file);
    
    // 备用URL
    const backupUrl = `${apiBaseUrl}/upload/image`;
    console.log('尝试备用上传URL:', backupUrl);
    
    const headers = {};
    if (authStore.token) {
      headers['Authorization'] = `Bearer ${authStore.token}`;
    }
    
    const response = await axios.post(backupUrl, formData, {
      headers: headers
    });
    
    console.log('备用上传成功:', response);
    
    // 处理响应
    if (response.data) {
      let imageUrl = '';
      
      if (response.data.errno === 0 && response.data.data && response.data.data.url) {
        imageUrl = response.data.data.url;
      } else if (response.data.imageUrl) {
        imageUrl = response.data.imageUrl;
      } else if (response.data.url) {
        imageUrl = response.data.url;
      }
      
      if (imageUrl) {
        paymentProofUrl.value = processImageUrl(imageUrl);
        ElMessage.success('支付截图上传成功（备用方式）');
      } else {
        ElMessage.error('无法从响应中提取图片URL');
      }
    }
  } catch (err) {
    console.error('备用上传失败:', err);
    ElMessage.error('备用上传方式也失败，请联系管理员');
  }
};

// 处理二维码点击
const handleQRCodeClick = () => {
  previewImageUrl.value = qrCodeUrl.value;
  isQRCodePreview.value = true;
  dialogVisible.value = true;
};

// 下载二维码
const downloadQRCode = () => {
  const link = document.createElement('a');
  link.href = qrCodeUrl.value;
  link.download = '支付宝收款码.png';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  ElMessage.success('二维码下载成功');
};

// 修改原有的预览处理函数
const handlePreview = (file) => {
  console.log('预览图片:', file);
  previewImageUrl.value = file.url || (file.response && file.response.data ? file.response.data.url : file.response && file.response.imageUrl ? file.response.imageUrl : '');
  isQRCodePreview.value = false;
  dialogVisible.value = true;
};

// 处理移除图片
const handleRemove = () => {
  console.log('移除支付截图');
  paymentProofUrl.value = '';
};

// 确认支付完成
const confirmPayment = () => {
  if (!paymentProofUrl.value) {
    ElMessage.warning('请先上传支付截图');
    return;
  }
  
  submitting.value = true;
  
  // 延迟操作模拟网络请求
  setTimeout(() => {
    submitting.value = false;
    
    // 触发支付确认事件，将支付截图URL传递给父组件
    emit('payment-confirmed', {
      paymentProofUrl: paymentProofUrl.value
    });
    
    ElMessage.success('支付信息提交成功');
  }, 1000);
};
</script>

<style scoped>
.payment-qrcode-container {
  padding: 20px;
  background: #f9f9f9;
  border-radius: 8px;
}

.payment-step {
  margin-bottom: 30px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.step-number {
  width: 28px;
  height: 28px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 10px;
}

.step-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.qrcode-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.qrcode-wrapper {
  width: 240px;
  height: 240px;
  padding: 10px;
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.qrcode-wrapper:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.qrcode-hover-tip {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  text-align: center;
  padding: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.qrcode-wrapper:hover .qrcode-hover-tip {
  opacity: 1;
}

.qrcode-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.payment-info {
  text-align: center;
  margin-top: 10px;
}

.payment-amount {
  font-size: 16px;
  color: #303133;
  margin-bottom: 10px;
}

.amount {
  font-weight: bold;
  color: #ff4d4f;
  font-size: 20px;
}

.payment-notice {
  color: #606266;
  margin-bottom: 6px;
}

.payment-tips {
  color: #e6a23c;
  font-size: 14px;
}

.upload-section {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.payment-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.preview-actions {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

@media (min-width: 768px) {
  .qrcode-section {
    flex-direction: row;
    justify-content: center;
    align-items: flex-start;
  }
  
  .payment-info {
    text-align: left;
    margin-top: 0;
    margin-left: 20px;
  }
}
</style> 