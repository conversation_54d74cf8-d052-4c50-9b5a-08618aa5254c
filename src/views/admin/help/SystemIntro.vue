<template>
  <div class="help-doc system-intro">
    <h2>系统介绍</h2>
    
    <el-card class="intro-card">
      <template #header>
        <div class="card-header">
          <h3>系统概述</h3>
        </div>
      </template>
      <div class="card-content">
        <p>光年小卖部管理系统是一套完整的企业内部商品兑换管理解决方案，专为企业内部积分商城设计。系统集成飞书登录认证，支持光年币和人民币双重支付方式，提供了完整的商品管理、订单处理、用户管理和数据分析功能。</p>

        <h4>核心功能模块</h4>
        <ul>
          <li><strong>数据仪表盘</strong>：实时展示销售数据、用户统计、热门商品等关键运营指标</li>
          <li><strong>商品管理</strong>：完整的商品生命周期管理，支持光年币和人民币双重定价</li>
          <li><strong>订单管理</strong>：处理用户兑换申请，支持多种支付方式和订单状态跟踪</li>
          <li><strong>分类管理</strong>：灵活的商品分类体系，支持多级分类结构</li>
          <li><strong>用户管理</strong>：基于飞书认证的用户体系，支持部门和职场管理</li>
          <li><strong>飞书机器人集成</strong>：自动推送销售报告、订单通知、里程碑庆祝等消息</li>
          <li><strong>反馈管理</strong>：收集和处理用户反馈，持续改进用户体验</li>
          <li><strong>公告管理</strong>：发布系统公告和重要通知</li>
          <li><strong>系统设置</strong>：通知配置、消息模板、智能调度等高级功能</li>
          <li><strong>数据导出</strong>：支持Excel、CSV等格式的数据导出，便于数据分析</li>
          <li><strong>日志管理</strong>：完整的操作日志记录，确保系统安全和可追溯性</li>
        </ul>
      </div>
    </el-card>
    
    <el-card class="intro-card">
      <template #header>
        <div class="card-header">
          <h3>系统特点</h3>
        </div>
      </template>
      <div class="card-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#409EFF"><Ship /></el-icon>
              <h4>飞书集成</h4>
              <p>深度集成飞书生态，支持飞书登录和群消息推送</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#67C23A"><DataAnalysis /></el-icon>
              <h4>双重支付</h4>
              <p>支持光年币和人民币两种支付方式，满足不同需求</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#E6A23C"><Connection /></el-icon>
              <h4>智能通知</h4>
              <p>自动推送销售报告、订单状态和重要里程碑</p>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#F56C6C"><Cellphone /></el-icon>
              <h4>移动端适配</h4>
              <p>完美支持各种移动设备，随时随地管理系统</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#909399"><ChatLineRound /></el-icon>
              <h4>多语言支持</h4>
              <p>支持中文、英文等多种语言，满足国际化需求</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="feature-item">
              <el-icon :size="40" color="#9254DE"><Lock /></el-icon>
              <h4>安全可靠</h4>
              <p>严格的权限控制和完整的操作日志，确保数据安全</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <el-card class="intro-card">
      <template #header>
        <div class="card-header">
          <h3>系统要求</h3>
        </div>
      </template>
      <div class="card-content">
        <h4>推荐浏览器</h4>
        <p>为了获得最佳使用体验，建议使用以下现代浏览器：</p>
        <ul>
          <li>Google Chrome 100+</li>
          <li>Mozilla Firefox 95+</li>
          <li>Microsoft Edge 100+</li>
          <li>Safari 15+</li>
        </ul>
        
        <h4>设备支持</h4>
        <p>本系统完全支持在以下设备上使用：</p>
        <ul>
          <li>桌面电脑（Windows、macOS、Linux）</li>
          <li>平板电脑（iPad、Android平板）</li>
          <li>手机（iPhone、Android手机）</li>
        </ul>
        
        <h4>网络要求</h4>
        <p>系统对网络要求如下：</p>
        <ul>
          <li>稳定的网络连接</li>
          <li>推荐带宽：1Mbps以上</li>
          <li>支持HTTPS安全协议</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Ship, DataAnalysis, Connection, Cellphone, ChatLineRound, Lock } from '@element-plus/icons-vue';
</script>

<style scoped>
.system-intro h2 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.intro-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.card-content {
  line-height: 1.6;
}

.card-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin-bottom: 12px;
  color: #606266;
}

.card-content ul {
  padding-left: 20px;
  margin-bottom: 16px;
}

.card-content li {
  margin-bottom: 6px;
  color: #606266;
}

.feature-item {
  text-align: center;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
  height: 100%;
}

.feature-item h4 {
  margin: 12px 0;
}

.feature-item p {
  font-size: 14px;
  color: #606266;
}
</style> 