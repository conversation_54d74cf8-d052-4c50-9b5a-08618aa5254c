const feishuService = require('../services/feishuService');
const { generateToken } = require('../utils/jwt');
const { logUserLogin } = require('./logController');

/**
 * 获取飞书登录URL
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLoginUrl = async (req, res) => {
  try {
    console.log('飞书控制器: 请求获取登录URL');

    // 生成随机状态以防止CSRF攻击
    const state = Math.random().toString(36).substring(2, 15);

    // 将状态保存在会话中（如果使用会话）
    req.session = req.session || {};
    req.session.feishuState = state;

    // 生成授权URL
    const authUrl = feishuService.generateAuthUrl(state);
    console.log('飞书控制器: 生成的授权URL:', authUrl);

    return res.status(200).json({
      message: '获取飞书登录URL成功',
      url: authUrl,
      state: state
    });
  } catch (error) {
    console.error('获取飞书登录URL错误:', error);
    return res.status(500).json({ message: '获取飞书登录URL失败', error: error.message });
  }
};

/**
 * 飞书登录回调处理
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.handleCallback = async (req, res) => {
  try {
    const { code, state } = req.query;

    console.log('飞书回调: 收到授权码和状态:', { code, state });

    if (!code) {
      console.error('飞书回调: 授权码不存在');
      return res.status(400).json({ message: '授权失败，未收到授权码' });
    }

    // 验证状态（防止CSRF攻击）
    const savedState = req.session?.feishuState;
    if (savedState && state !== savedState) {
      console.error('飞书回调: 状态不匹配', { savedState, receivedState: state });
      return res.status(400).json({ message: '授权失败，状态参数不匹配' });
    }

    // 使用授权码获取访问令牌
    console.log('飞书回调: 获取访问令牌...');
    const tokenResponse = await feishuService.getAccessToken(code);
    console.log('飞书回调: 访问令牌获取成功');

    // 飞书新版API响应格式：{ code: 0, data: { access_token: '...' } }
    if (tokenResponse.code !== 0 || !tokenResponse.data?.access_token) {
      console.error('飞书回调: 访问令牌获取失败', tokenResponse);
      return res.status(400).json({ message: '获取访问令牌失败' });
    }

    // 获取用户信息
    console.log('飞书回调: 获取用户信息...');
    const userInfoResponse = await feishuService.getUserInfo(tokenResponse.data.access_token);
    console.log('飞书回调: 用户信息获取成功');

    // 飞书新版API响应格式：{ code: 0, data: { ... } }
    if (userInfoResponse.code !== 0 || !userInfoResponse.data) {
      console.error('飞书回调: 用户信息获取失败', userInfoResponse);
      return res.status(400).json({ message: '获取用户信息失败' });
    }

    let userDataWithDepartment = userInfoResponse.data;

    // 获取用户详细信息（包含部门ID）
    try {
      console.log('飞书回调: 获取用户详细信息（包含部门ID）...');
      const userDetailResponse = await feishuService.getUserDetailInfo(
        tokenResponse.data.access_token,
        userInfoResponse.data.open_id
      );

      if (userDetailResponse.code === 0 && userDetailResponse.data?.user) {
        console.log('飞书回调: 用户详细信息获取成功');

        // 将详细信息中的部门ID合并到用户数据中
        if (userDetailResponse.data.user.department_ids) {
          userDataWithDepartment.department_ids = userDetailResponse.data.user.department_ids;
          console.log(`飞书回调: 获取到用户部门ID: ${JSON.stringify(userDetailResponse.data.user.department_ids)}`);
        }

        // 合并其他有用的详细信息
        if (userDetailResponse.data.user.enterprise_email) {
          userDataWithDepartment.enterprise_email = userDetailResponse.data.user.enterprise_email;
        }

        if (userDetailResponse.data.user.mobile) {
          userDataWithDepartment.mobile = userDetailResponse.data.user.mobile;
        }

        if (userDetailResponse.data.user.city) {
          userDataWithDepartment.city = userDetailResponse.data.user.city;
        }
      } else {
        console.log('飞书回调: 用户详细信息获取失败，继续使用基础信息');
      }
    } catch (detailError) {
      console.error('飞书回调: 获取用户详细信息出错，继续使用基础信息:', detailError.message);
    }

    // 检查关键信息是否存在
    console.log('飞书回调: 最终用户信息:');
    console.log(`- 用户名: ${userDataWithDepartment.name || '未提供'}`);
    console.log(`- 邮箱: ${userDataWithDepartment.email || '未提供'}`);
    console.log(`- 企业邮箱: ${userDataWithDepartment.enterprise_email || '未提供'}`);
    console.log(`- 手机号: ${userDataWithDepartment.mobile || '未提供'}`);
    console.log(`- 城市: ${userDataWithDepartment.city || '未提供'}`);

    if (userDataWithDepartment.department_ids && userDataWithDepartment.department_ids.length > 0) {
      console.log(`- 部门ID列表: ${JSON.stringify(userDataWithDepartment.department_ids)}`);
      console.log(`- 主要部门ID: ${userDataWithDepartment.department_ids[0]}`);
    } else {
      console.log('- 部门ID: 未提供或为空');
    }

    // 创建或更新用户
    console.log('飞书回调: 创建或更新用户...');
    const user = await feishuService.createOrUpdateFeishuUser(
      userDataWithDepartment,
      tokenResponse.data,
      req  // 传入请求对象以获取IP地址
    );
    console.log('飞书回调: 用户创建或更新成功');
    console.log(`飞书回调: 用户ID: ${user.id}, 用户名: ${user.username}, 部门: ${user.department || '未设置'}`);

    // 生成JWT令牌
    console.log('飞书回调: 生成JWT令牌...');
    // 移除密码信息
    const userForToken = { ...user.get() };
    delete userForToken.password;

    const token = generateToken(userForToken, true); // 使用长期令牌
    console.log('飞书回调: JWT令牌生成成功');

    // 记录用户登录
    await logUserLogin(user, req, 'feishu');

    // 返回成功响应（前端JS处理）
    const htmlResponse = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>飞书登录成功</title>
        <script>
          // 将令牌存储在会话存储中
          window.sessionStorage.setItem('token', '${token}');

          // 将用户信息存储为JSON字符串
          const user = ${JSON.stringify(userForToken)};
          window.sessionStorage.setItem('user', JSON.stringify(user));

          // 通知父窗口登录成功
          if (window.opener) {
            window.opener.postMessage({ type: 'feishu-login-success', token: '${token}', user: user }, '*');
            window.close();
          } else {
            // 如果没有父窗口，则重定向到首页
            window.location.href = '/';
          }
        </script>
      </head>
      <body>
        <h2>飞书登录成功</h2>
        <p>正在跳转...</p>
      </body>
      </html>
    `;

    res.set('Content-Type', 'text/html');
    return res.send(htmlResponse);
  } catch (error) {
    console.error('飞书回调处理错误:', error);

    // 特别处理账号禁用的情况
    if (error.message === '账号已被禁用，请联系管理员') {
      const errorHtml = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>登录失败</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error-container { max-width: 500px; margin: 0 auto; }
            .error-title { color: #f56c6c; }
            .error-message { margin: 20px 0; }
            .error-code { color: #909399; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="error-container">
            <h2 class="error-title">登录失败</h2>
            <p class="error-message">账号已被禁用，请联系管理员</p>
            <p class="error-code">错误代码: ACCOUNT_DISABLED</p>
            <button onclick="window.close()">关闭窗口</button>
          </div>
        </body>
        </html>
      `;

      res.set('Content-Type', 'text/html');
      return res.send(errorHtml);
    }

    return res.status(500).json({ message: '飞书登录失败', error: error.message });
  }
};

/**
 * 飞书登录直接处理（用于前端直接飞书登录）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.feishuLogin = async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({ message: '授权码不存在' });
    }

    // 使用授权码获取访问令牌
    const tokenResponse = await feishuService.getAccessToken(code);

    if (tokenResponse.code !== 0 || !tokenResponse.data?.access_token) {
      return res.status(400).json({ message: '获取访问令牌失败' });
    }

    // 获取用户信息
    const userInfoResponse = await feishuService.getUserInfo(tokenResponse.data.access_token);

    if (userInfoResponse.code !== 0 || !userInfoResponse.data) {
      return res.status(400).json({ message: '获取用户信息失败' });
    }

    let userDataWithDepartment = userInfoResponse.data;

    // 获取用户详细信息（包含部门ID）
    try {
      console.log('飞书登录: 获取用户详细信息（包含部门ID）...');
      const userDetailResponse = await feishuService.getUserDetailInfo(
        tokenResponse.data.access_token,
        userInfoResponse.data.open_id
      );

      if (userDetailResponse.code === 0 && userDetailResponse.data?.user) {
        console.log('飞书登录: 用户详细信息获取成功');

        // 将详细信息中的部门ID合并到用户数据中
        if (userDetailResponse.data.user.department_ids) {
          userDataWithDepartment.department_ids = userDetailResponse.data.user.department_ids;
          console.log(`飞书登录: 获取到用户部门ID: ${JSON.stringify(userDetailResponse.data.user.department_ids)}`);
        }

        // 合并其他有用的详细信息
        if (userDetailResponse.data.user.enterprise_email) {
          userDataWithDepartment.enterprise_email = userDetailResponse.data.user.enterprise_email;
        }

        if (userDetailResponse.data.user.mobile) {
          userDataWithDepartment.mobile = userDetailResponse.data.user.mobile;
        }

        if (userDetailResponse.data.user.city) {
          userDataWithDepartment.city = userDetailResponse.data.user.city;
        }
      } else {
        console.log('飞书登录: 用户详细信息获取失败，继续使用基础信息');
      }
    } catch (detailError) {
      console.error('飞书登录: 获取用户详细信息出错，继续使用基础信息:', detailError.message);
    }

    // 创建或更新用户
    const user = await feishuService.createOrUpdateFeishuUser(
      userDataWithDepartment,
      tokenResponse.data,
      req  // 传入请求对象以获取IP地址
    );

    // 生成JWT令牌
    const userForToken = { ...user.get() };
    delete userForToken.password;

    const token = generateToken(userForToken, true); // 使用长期令牌

    // 记录用户登录
    await logUserLogin(user, req, 'feishu');

    // 返回用户信息和令牌
    return res.status(200).json({
      message: '飞书登录成功',
      user: userForToken,
      token
    });
  } catch (error) {
    console.error('飞书登录错误:', error);

    // 特别处理账号禁用的情况
    if (error.message === '账号已被禁用，请联系管理员') {
      return res.status(403).json({
        message: '账号已被禁用，请联系管理员',
        code: 'ACCOUNT_DISABLED'
      });
    }

    return res.status(500).json({ message: '飞书登录失败', error: error.message });
  }
};
