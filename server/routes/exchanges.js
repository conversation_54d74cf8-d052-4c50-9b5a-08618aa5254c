const express = require('express');
const router = express.Router();
const exchangeController = require('../controllers/exchangeController');
const { authenticate } = require('../middlewares/authMiddleware');
const { isAdmin } = require('../middlewares/admin');

// 创建兑换申请
router.post('/', authenticate, exchangeController.createExchange);

// 获取当前用户的兑换记录
router.get('/user', authenticate, exchangeController.getUserExchanges);

// 获取当前用户最近一次兑换记录
router.get('/user/last', authenticate, exchangeController.getUserLastExchange);

// 获取最近的兑换订单（仅管理员）
router.get('/recent', authenticate, isAdmin, exchangeController.getRecentExchanges);

// 导出订单数据（仅管理员）
router.get('/export', authenticate, isAdmin, exchangeController.exportExchanges);

// 导入订单数据（仅管理员）
router.post('/import', authenticate, isAdmin, exchangeController.importExchanges);

// 批量删除订单（仅管理员）
router.delete('/batch', authenticate, isAdmin, exchangeController.batchDeleteExchanges);

// 检查并重置自增ID（仅管理员）
router.post('/reset-auto-increment', authenticate, isAdmin, exchangeController.checkAndResetAutoIncrement);

// 获取订单导入模板（仅管理员）
router.get('/template', authenticate, isAdmin, exchangeController.getExchangeTemplate);

// 获取职场分布统计数据（仅管理员）
router.get('/workplace-stats', authenticate, isAdmin, exchangeController.getWorkplaceExchangeStats);

// 获取仪表盘综合统计数据（仅管理员）
router.get('/dashboard-stats', authenticate, isAdmin, exchangeController.getDashboardStats);

// 获取销售趋势数据（仅管理员）
router.get('/sales-trend', authenticate, isAdmin, exchangeController.getSalesTrend);

// 获取库存变化趋势数据（仅管理员）
router.get('/stock-trend', authenticate, isAdmin, exchangeController.getStockTrend);

// 获取兑换记录详情
router.get('/:id', authenticate, exchangeController.getExchangeDetail);

// 取消兑换申请
router.put('/:id/cancel', authenticate, exchangeController.cancelExchange);

// 获取所有兑换申请(带分页、筛选) - 仅管理员
router.get('/', authenticate, isAdmin, exchangeController.getExchangeList);

// 管理员更新兑换订单状态
router.put('/:id/status', authenticate, isAdmin, exchangeController.updateExchangeStatus);

// 用户更新联系方式
router.put('/:id/contact-info', authenticate, exchangeController.updateContactInfo);

// 管理员更新联系方式
router.put('/:id/admin/contact-info', authenticate, isAdmin, exchangeController.adminUpdateContactInfo);

// 调整订单价格（仅限管理员）
router.put('/:id/adjust-price', authenticate, isAdmin, exchangeController.adjustPrice);

module.exports = router;
